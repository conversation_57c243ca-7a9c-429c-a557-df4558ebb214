<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary"
    tools:context=".activities.SettingsActivity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/headerContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/background_secondary"
        android:elevation="2dp"
        android:paddingHorizontal="16dp"
        android:paddingVertical="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/backButton"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_arrow_back"
            android:tint="@color/icon_tint"
            android:contentDescription="@string/new_reminder_backButton_contentDescription"
            android:clickable="true"
            android:focusable="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/titleText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/settings_title"
            android:textColor="@color/text_primary"
            android:textSize="@dimen/texto_boton"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/headerContainer">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:id="@+id/notificationsContainer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:paddingVertical="12dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:background="?android:attr/selectableItemBackground">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="16dp"
                            android:src="@drawable/ic_notifications" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/settings_notifications"
                                android:textColor="@color/text_primary"
                                android:textSize="@dimen/texto_normal"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/settings_notifications_desc"
                                android:textColor="@color/text_color_secondary"
                                android:textSize="@dimen/text_size_medium" />

                        </LinearLayout>

                        <Switch
                            android:id="@+id/notificationsSwitch"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/darkModeContainer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:paddingVertical="12dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:background="?android:attr/selectableItemBackground">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="16dp"
                            android:src="@drawable/ic_dark_mode" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/settings_dark_mode"
                                android:textColor="@color/text_primary"
                                android:textSize="@dimen/texto_normal"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/settings_dark_mode_desc"
                                android:textColor="@color/text_color_secondary"
                                android:textSize="@dimen/text_size_medium" />

                        </LinearLayout>

                        <Switch
                            android:id="@+id/darkModeSwitch"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="false" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/languageContainer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:paddingVertical="12dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:background="?android:attr/selectableItemBackground">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="16dp"
                            android:src="@drawable/ic_language" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/settings_language"
                                android:textColor="@color/text_primary"
                                android:textSize="@dimen/texto_normal"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/settings_language_desc"
                                android:textColor="@color/text_color_secondary"
                                android:textSize="@dimen/text_size_medium" />

                        </LinearLayout>

                        <Spinner
                            android:id="@+id/languageSpinner"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:minWidth="100dp"
                            android:entries="@array/language_options" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:id="@+id/privacyContainer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:paddingVertical="12dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:background="?android:attr/selectableItemBackground">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="16dp"
                            android:src="@drawable/ic_privacy" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/settings_privacy"
                                android:textColor="@color/text_primary"
                                android:textSize="@dimen/texto_normal"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/settings_privacy_desc"
                                android:textColor="@color/text_color_secondary"
                                android:textSize="@dimen/text_size_medium" />

                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/profileContainer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:paddingVertical="12dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:background="?android:attr/selectableItemBackground">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="16dp"
                            android:src="@drawable/ic_profile" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/settings_profile"
                                android:textColor="@color/text_primary"
                                android:textSize="@dimen/texto_normal"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/settings_profile_desc"
                                android:textColor="@color/text_color_secondary"
                                android:textSize="@dimen/text_size_medium" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="32dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:id="@+id/logoutContainer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:background="?android:attr/selectableItemBackground">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="16dp"
                        android:src="@drawable/ic_logout" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/settings_logout"
                            android:textColor="#F44336"
                            android:textSize="@dimen/texto_normal"
                            android:textStyle="bold" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:layout_marginTop="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/settings_app_version"
                    android:textColor="@color/text_color_secondary"
                    android:textSize="@dimen/text_size_medium"
                    android:gravity="center" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/settings_copyright"
                    android:textColor="@color/text_color_secondary"
                    android:textSize="12sp"
                    android:gravity="center"
                    android:layout_marginTop="4dp" />

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>

<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary"
    tools:context=".activities.TermsActivity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/headerContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/background_secondary"
        android:elevation="2dp"
        android:paddingHorizontal="16dp"
        android:paddingVertical="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/backButton"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_arrow_back"
            android:tint="@color/icon_tint"
            android:contentDescription="@string/new_reminder_backButton_contentDescription"
            android:clickable="true"
            android:focusable="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/titleText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/terms_title"
            android:textColor="@color/text_primary"
            android:textSize="@dimen/texto_boton"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="16dp"
        app:layout_constraintBottom_toTopOf="@id/acceptButton"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/headerContainer">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/terms_content_title"
                android:textColor="@color/text_primary"
                android:textSize="@dimen/texto_titulo_login"
                android:textStyle="bold"
                android:gravity="center"
                android:layout_marginBottom="24dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/terms_welcome"
                android:textColor="@color/text_primary"
                android:textSize="@dimen/texto_normal"
                android:lineSpacingExtra="4dp"
                android:layout_marginBottom="20dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/terms_section_1_title"
                android:textColor="@color/text_primary"
                android:textSize="@dimen/texto_normal"
                android:textStyle="bold"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/terms_section_1_content"
                android:textColor="@color/text_color_secondary"
                android:textSize="@dimen/texto_normal"
                android:lineSpacingExtra="4dp"
                android:layout_marginBottom="16dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/terms_section_2_title"
                android:textColor="@color/text_primary"
                android:textSize="@dimen/texto_normal"
                android:textStyle="bold"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/terms_section_2_content"
                android:textColor="@color/text_color_secondary"
                android:textSize="@dimen/texto_normal"
                android:lineSpacingExtra="4dp"
                android:layout_marginBottom="16dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/terms_section_3_title"
                android:textColor="@color/text_primary"
                android:textSize="@dimen/texto_normal"
                android:textStyle="bold"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/terms_section_3_content"
                android:textColor="@color/text_color_secondary"
                android:textSize="@dimen/texto_normal"
                android:lineSpacingExtra="4dp"
                android:layout_marginBottom="16dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/terms_section_4_title"
                android:textColor="@color/text_primary"
                android:textSize="@dimen/texto_normal"
                android:textStyle="bold"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/terms_section_4_content"
                android:textColor="@color/text_color_secondary"
                android:textSize="@dimen/texto_normal"
                android:lineSpacingExtra="4dp"
                android:layout_marginBottom="16dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/terms_section_5_title"
                android:textColor="@color/text_primary"
                android:textSize="@dimen/texto_normal"
                android:textStyle="bold"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/terms_section_5_content"
                android:textColor="@color/text_color_secondary"
                android:textSize="@dimen/texto_normal"
                android:lineSpacingExtra="4dp"
                android:layout_marginBottom="16dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/terms_section_6_title"
                android:textColor="@color/text_primary"
                android:textSize="@dimen/texto_normal"
                android:textStyle="bold"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/terms_section_6_content"
                android:textColor="@color/text_color_secondary"
                android:textSize="@dimen/texto_normal"
                android:lineSpacingExtra="4dp"
                android:layout_marginBottom="16dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/terms_section_7_title"
                android:textColor="@color/text_primary"
                android:textSize="@dimen/texto_normal"
                android:textStyle="bold"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/terms_section_7_content"
                android:textColor="@color/text_color_secondary"
                android:textSize="@dimen/texto_normal"
                android:lineSpacingExtra="4dp"
                android:layout_marginBottom="32dp" />

        </LinearLayout>

    </ScrollView>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/acceptButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="24dp"
        android:layout_marginBottom="24dp"
        android:text="@string/terms_accept_button"
        android:textColor="@color/white"
        android:textSize="@dimen/texto_boton"
        android:textStyle="bold"
        android:background="@drawable/button_save_style"
        app:backgroundTint="@null"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>

{"logs": [{"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.grupo3.medrem.app-mergeDebugResources-30:\\values-en\\values-en.xml", "map": [{"source": "C:\\Users\\<USER>\\Documents\\SISE\\Aplicaciones Moviles\\Examen Final\\am-g3-medrem\\app\\src\\main\\res\\values-en\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,61,60,54,55,56,53,64,63,62,57,32,34,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,86,87,-1,89,80,94,90,95,96,93,91,92,85,79,-1,84,76,77,88,78,-1,82,-1,-1,-1,-1,15,14,19,18,17,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,69,68,-1,-1,103,102,105,104,107,106,109,108,111,110,113,112,115,114,-1,101,67", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,4,4,4,4,4,4,4,4,4,4,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,-1,4,4,4,4,4,4,4,4,4,4,4,-1,4,4,4,4,4,-1,4,-1,-1,-1,-1,4,4,4,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,-1,-1,4,4,4,4,4,4,4,4,4,4,4,4,4,4,-1,4,4", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,3518,3470,3030,3102,3174,2954,3705,3655,3572,3261,1768,1906,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4829,4904,-1,5041,4469,5318,5096,5372,5428,5262,5150,5205,4761,4410,-1,4698,4201,4270,4990,4347,-1,4580,-1,-1,-1,-1,757,691,1089,960,886,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,3880,3833,-1,-1,5877,5801,6214,6144,6558,6490,6895,6820,7179,7112,7442,7377,7758,7690,-1,5663,3790", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,53,47,71,71,86,75,55,49,82,55,70,66,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,74,85,-1,54,59,53,53,55,53,55,54,56,67,58,-1,62,68,76,50,62,-1,56,-1,-1,-1,-1,127,65,57,128,73,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,48,46,-1,-1,266,75,275,69,261,67,216,74,197,66,247,64,212,67,-1,137,42", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,3567,3513,3097,3169,3256,3025,3756,3700,3650,3312,1834,1968,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4899,4985,-1,5091,4524,5367,5145,5423,5477,5313,5200,5257,4824,4464,-1,4756,4265,4342,5036,4405,-1,4632,-1,-1,-1,-1,880,752,1142,1084,955,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,3924,3875,-1,-1,6139,5872,6485,6209,6815,6553,7107,6890,7372,7174,7685,7437,7966,7753,-1,5796,3828"}, "to": {"startLines": "2,6,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,337,385,434,485,568,629,699,753,801,873,945,1032,1108,1164,1214,1297,1353,1424,1491,1558,1612,1664,1720,1791,1843,1916,1974,2049,2098,2161,2214,2306,2357,2432,2518,2594,2649,2709,2763,2817,2873,2927,2983,3038,3095,3163,3222,3283,3346,3415,3492,3543,3606,3657,3714,3774,3827,3970,4047,4175,4241,4299,4428,4502,4554,4620,4676,4742,4838,4907,4965,5033,5091,5148,5208,5299,5351,5409,5475,5562,5619,5693,5748,5827,5889,5951,6004,6081,6146,6230,6283,6361,6414,6492,6544,6612,6661,6708,6769,6845,7112,7188,7464,7534,7796,7864,8081,8156,8354,8421,8669,8734,8947,9015,9076,9214", "endLines": "5,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "endColumns": "19,19,47,48,50,82,60,69,53,47,71,71,86,75,55,49,82,55,70,66,66,53,51,55,70,51,72,57,74,48,62,52,91,50,74,85,75,54,59,53,53,55,53,55,54,56,67,58,60,62,68,76,50,62,50,56,59,52,142,76,127,65,57,128,73,51,65,55,65,95,68,57,67,57,56,59,90,51,57,65,86,56,73,54,78,61,61,52,76,64,83,52,77,52,77,51,67,48,46,60,75,266,75,275,69,261,67,216,74,197,66,247,64,212,67,60,137,42", "endOffsets": "159,332,380,429,480,563,624,694,748,796,868,940,1027,1103,1159,1209,1292,1348,1419,1486,1553,1607,1659,1715,1786,1838,1911,1969,2044,2093,2156,2209,2301,2352,2427,2513,2589,2644,2704,2758,2812,2868,2922,2978,3033,3090,3158,3217,3278,3341,3410,3487,3538,3601,3652,3709,3769,3822,3965,4042,4170,4236,4294,4423,4497,4549,4615,4671,4737,4833,4902,4960,5028,5086,5143,5203,5294,5346,5404,5470,5557,5614,5688,5743,5822,5884,5946,5999,6076,6141,6225,6278,6356,6409,6487,6539,6607,6656,6703,6764,6840,7107,7183,7459,7529,7791,7859,8076,8151,8349,8416,8664,8729,8942,9010,9071,9209,9252"}}]}, {"outputFile": "com.grupo3.medrem.app-mergeDebugResources-30:/values-en/values-en.xml", "map": [{"source": "C:\\Users\\<USER>\\Documents\\SISE\\Aplicaciones Moviles\\Examen Final\\am-g3-medrem\\app\\src\\main\\res\\values-en\\strings.xml", "from": {"startLines": "-1,-1,-1,138,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,141,140,143,142,139,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,4,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,9368,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,9544,9484,9654,9599,9433,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,63,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,53,58,55,53,49,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,9427,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,9593,9538,9705,9648,9478,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,6,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,337,385,449,498,549,632,693,763,817,865,937,1009,1096,1172,1228,1278,1361,1417,1471,1530,1586,1640,1690,1761,1828,1895,1949,2001,2057,2128,2180,2253,2311,2386,2435,2498,2551,2643,2694,2769,2855,2931,2986,3046,3100,3154,3210,3264,3320,3375,3432,3500,3559,3620,3683,3752,3829,3880,3943,3994,4051,4111,4164,4307,4384,4512,4578,4636,4765,4839,4891,4957,5013,5079,5175,5244,5302,5370,5428,5485,5545,5636,5688,5746,5812,5899,5956,6030,6085,6164,6226,6288,6341,6418,6483,6567,6620,6698,6751,6829,6881,6949,6998,7045,7106,7182,7449,7525,7801,7871,8133,8201,8418,8493,8691,8758,9006,9071,9284,9352,9413,9551", "endLines": "5,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131", "endColumns": "19,19,47,63,48,50,82,60,69,53,47,71,71,86,75,55,49,82,55,53,58,55,53,49,70,66,66,53,51,55,70,51,72,57,74,48,62,52,91,50,74,85,75,54,59,53,53,55,53,55,54,56,67,58,60,62,68,76,50,62,50,56,59,52,142,76,127,65,57,128,73,51,65,55,65,95,68,57,67,57,56,59,90,51,57,65,86,56,73,54,78,61,61,52,76,64,83,52,77,52,77,51,67,48,46,60,75,266,75,275,69,261,67,216,74,197,66,247,64,212,67,60,137,42", "endOffsets": "159,332,380,444,493,544,627,688,758,812,860,932,1004,1091,1167,1223,1273,1356,1412,1466,1525,1581,1635,1685,1756,1823,1890,1944,1996,2052,2123,2175,2248,2306,2381,2430,2493,2546,2638,2689,2764,2850,2926,2981,3041,3095,3149,3205,3259,3315,3370,3427,3495,3554,3615,3678,3747,3824,3875,3938,3989,4046,4106,4159,4302,4379,4507,4573,4631,4760,4834,4886,4952,5008,5074,5170,5239,5297,5365,5423,5480,5540,5631,5683,5741,5807,5894,5951,6025,6080,6159,6221,6283,6336,6413,6478,6562,6615,6693,6746,6824,6876,6944,6993,7040,7101,7177,7444,7520,7796,7866,8128,8196,8413,8488,8686,8753,9001,9066,9279,9347,9408,9546,9589"}}]}]}
<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.grupo3.medrem"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="35" />

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <permission
        android:name="com.grupo3.medrem.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.grupo3.medrem.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:name="com.grupo3.medrem.MedRemApplication"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:testOnly="true"
        android:theme="@style/Theme.MedRem"
        android:usesCleartextTraffic="true" >
        <activity
            android:name="com.grupo3.medrem.activities.HistoryActivity"
            android:exported="false" />
        <activity
            android:name="com.grupo3.medrem.activities.SettingsActivity"
            android:exported="false" />
        <activity
            android:name="com.grupo3.medrem.activities.TermsActivity"
            android:exported="false" />
        <activity
            android:name="com.grupo3.medrem.activities.NewReminderActivity"
            android:exported="false" />
        <activity
            android:name="com.grupo3.medrem.activities.DashboardActivity"
            android:exported="false" />
        <activity
            android:name="com.grupo3.medrem.activities.RegisterActivity"
            android:exported="false" />
        <activity
            android:name="com.grupo3.medrem.activities.LoginActivity"
            android:exported="false" />
        <activity
            android:name="com.grupo3.medrem.activities.Onboarding4Activity"
            android:exported="false" />
        <activity
            android:name="com.grupo3.medrem.activities.Onboarding3Activity"
            android:exported="false" />
        <activity
            android:name="com.grupo3.medrem.activities.Onboarding2Activity"
            android:exported="false" />
        <activity
            android:name="com.grupo3.medrem.activities.SplashActivity"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.grupo3.medrem.activities.MainActivity"
            android:exported="false" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.grupo3.medrem.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>
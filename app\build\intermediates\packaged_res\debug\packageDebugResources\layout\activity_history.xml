<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary"
    tools:context=".activities.HistoryActivity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/headerContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/background_secondary"
        android:paddingHorizontal="16dp"
        android:paddingVertical="16dp"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/backButton"
            android:layout_width="@dimen/icon_size_medium"
            android:layout_height="@dimen/icon_size_medium"
            android:src="@drawable/ic_arrow_back"
            android:tint="@color/icon_tint"
            android:contentDescription="@string/back_button_contentDescription"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <TextView
            android:id="@+id/titleText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/history_title"
            android:textColor="@color/text_primary"
            android:textSize="@dimen/texto_boton"
            android:textStyle="bold"
            app:layout_constraintStart_toEndOf="@id/backButton"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/headerContainer"
        app:layout_constraintBottom_toBottomOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/margin_card_horizontal">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingVertical="@dimen/margin_text_start">

                <TextView
                    android:id="@+id/monthText"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/history_month_example"
                    android:textColor="@color/text_primary"
                    android:textSize="@dimen/texto_mediano"
                    android:textStyle="bold"/>

                <Button
                    android:id="@+id/calendarButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/history_calendar"
                    android:textColor="@color/blue"
                    android:backgroundTint="@color/background_secondary"
                    android:elevation="0dp"/>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:background="@color/background_secondary"
                android:elevation="2dp"
                android:padding="@dimen/margin_card_horizontal">

                <TextView
                    android:id="@+id/tabTaken"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@color/colorTaken"
                    android:gravity="center"
                    android:paddingVertical="@dimen/margin_card_vertical"
                    android:text="@string/history_tab_taken"
                    android:textColor="@color/color_success"
                    android:textSize="@dimen/texto_normal"
                    android:textStyle="bold"/>

                <TextView
                    android:id="@+id/tabMissed"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@color/colorMissed"
                    android:gravity="center"
                    android:paddingVertical="@dimen/margin_card_vertical"
                    android:text="@string/history_tab_missed"
                    android:textColor="@color/color_error"
                    android:textSize="@dimen/texto_normal"
                    android:textStyle="bold"/>
            </LinearLayout>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginVertical="@dimen/margin_card_vertical"
                app:cardCornerRadius="@dimen/card_corner_radius"
                app:cardElevation="@dimen/card_elevation">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:background="@color/colorTaken"
                    android:padding="@dimen/padding_card">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Paracetamol"
                        android:textColor="@color/text_primary"
                        android:textSize="@dimen/text_size_large"
                        android:textStyle="bold"/>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="500 mg"
                        android:textColor="@color/text_color_secondary"
                        android:textSize="@dimen/text_size_medium"/>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginTop="@dimen/margin_text_start">


                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Hoy, 08:00"
                            android:textColor="@color/text_color_secondary"
                            android:textSize="@dimen/text_size_medium"/>

                        <ImageView
                            android:layout_width="@dimen/icon_size_medium"
                            android:layout_height="@dimen/icon_size_medium"
                            android:src="@drawable/ic_check_circle"
                            android:tint="@color/color_success"/>
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

        </LinearLayout>
    </ScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>
